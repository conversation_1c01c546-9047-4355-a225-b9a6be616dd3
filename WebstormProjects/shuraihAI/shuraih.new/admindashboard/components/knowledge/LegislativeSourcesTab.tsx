/**
 * لسان التبويب الخاص بإدارة المصادر التشريعية المنظمة
 * =============================================================
 *
 * يعرض جدولاً للمصادر التشريعية مع وظائف البحث، الفلترة،
 * الإضافة، التعديل، والحذف.
 *
 * المؤلف: فريق تطوير شُرَيح
 * التاريخ: 23 يوليو 2024
 */

import React, { useState, useEffect } from 'react';
import {
  Table, Card, Input, Select, Button, Space, Tooltip, Popconfirm, Tag, message, Badge, Modal, Form, Drawer, Descriptions, Divider, Row, Col, Typography,
} from 'antd';
import type { TableProps } from 'antd';
import {
  PlusOutlined, SearchOutlined, FilterOutlined, EyeOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, InboxOutlined, ReloadOutlined,
} from '@ant-design/icons';
import { knowledgeService, LegislativeSource, DataSourceStatus, CreateLegislativeSourceRequest, UpdateLegislativeSourceRequest } from '../../services/knowledge-service';
import dayjs from 'dayjs';
import { GraphVisualizer } from './GraphVisualizer';
import { HistoryVisualizer } from './HistoryVisualizer';

const { Option } = Select;
const { Title, Paragraph, Text } = Typography;

export const LegislativeSourcesTab: React.FC = () => {
  const [sources, setSources] = useState<LegislativeSource[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  // Modal and Drawer states
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [editingSource, setEditingSource] = useState<LegislativeSource | null>(null);
  const [viewingSource, setViewingSource] = useState<LegislativeSource | null>(null);
  const [form] = Form.useForm();

  // Filter states
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<DataSourceStatus | null>(null);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);

  const fetchSources = async () => {
    setLoading(true);
    try {
      const response = await knowledgeService.getLegislativeSources({
        page: currentPage,
        limit: pageSize,
        search: searchText,
        status: statusFilter || undefined,
        category: categoryFilter || undefined,
      });
      // NOTE: Using mock data until backend is fully integrated
      if (!response || response.items.length === 0) {
        const mockData: LegislativeSource[] = [{ id: '1', title: 'المادة 77 من نظام العمل', content: '...', source_document: 'نظام العمل', category: 'العمل', tags: [], status: 'approved', usage_count: 152, created_at: new Date().toISOString(), updated_at: new Date().toISOString(), created_by: 'admin', version: 1 }];
        setSources(mockData);
        setTotal(mockData.length);
      } else {
        setSources(response.items);
        setTotal(response.total);
      }
    } catch (error) {
      message.error('فشل في جلب المصادر التشريعية. عرض بيانات تجريبية.');
      const mockData: LegislativeSource[] = [{ id: '1', title: 'المادة 77 من نظام العمل', content: '...', source_document: 'نظام العمل', category: 'العمل', tags: [], status: 'approved', usage_count: 152, created_at: new Date().toISOString(), updated_at: new Date().toISOString(), created_by: 'admin', version: 1 }];
      setSources(mockData);
      setTotal(mockData.length);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSources();
  }, [currentPage, pageSize, searchText, statusFilter, categoryFilter]);
  
  // --- Modal and Form Handling ---
  const showAddModal = () => {
    setEditingSource(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const showEditModal = (record: LegislativeSource) => {
    setEditingSource(record);
    form.setFieldsValue({
        ...record,
        tags: record.tags.join(','),
    });
    setIsModalVisible(true);
  };

  const showViewDrawer = (record: LegislativeSource) => {
    setViewingSource(record);
    setIsDrawerVisible(true);
  };
  
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingSource(null);
  };

  const onFinish = async (values: any) => {
    try {
        const payload = {
            ...values,
            tags: values.tags ? values.tags.split(',').map((t: string) => t.trim()) : [],
        };

        if (editingSource) {
            await knowledgeService.updateLegislativeSource(editingSource.id, payload as UpdateLegislativeSourceRequest);
            message.success('تم تحديث المصدر بنجاح');
        } else {
            await knowledgeService.createLegislativeSource(payload as CreateLegislativeSourceRequest);
            message.success('تم إنشاء المصدر بنجاح');
        }
        setIsModalVisible(false);
        fetchSources(); // Refresh table
    } catch (error) {
        message.error('حدث خطأ أثناء حفظ المصدر');
    }
  };
  
  const handleDelete = async (id: string) => {
    try {
        await knowledgeService.deleteLegislativeSource(id);
        message.success('تم حذف المصدر بنجاح');
        fetchSources(); // Refresh table
    } catch (error) {
        message.error('حدث خطأ أثناء حذف المصدر');
    }
  };

  const handleApprove = async (id: string) => {
    try {
      await knowledgeService.approveLegislativeSource(id);
      message.success('تم اعتماد المصدر بنجاح');
      fetchSources();
    } catch (error) {
      message.error('فشل في اعتماد المصدر');
    }
  };

  // --- UI Helpers ---
  const getStatusTag = (status: DataSourceStatus) => {
    switch (status) {
      case 'pending_review':
        return <Tag icon={<ClockCircleOutlined />} color="warning">قيد المراجعة</Tag>;
      case 'approved':
        return <Tag icon={<CheckCircleOutlined />} color="success">معتمد</Tag>;
      case 'rejected':
        return <Tag icon={<CloseCircleOutlined />} color="error">مرفوض</Tag>;
      case 'archived':
        return <Tag icon={<InboxOutlined />} color="default">مؤرشف</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const columns: TableProps<LegislativeSource>['columns'] = [
    {
      title: 'العنوان',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: LegislativeSource) => (
        <div>
          <a style={{ fontWeight: 500 }}>{record.article_number ? `المادة ${record.article_number}: ${text}` : text}</a>
          <div style={{ fontSize: '12px', color: '#888' }}>
            من: {record.source_document}
          </div>
        </div>
      )
    },
    {
      title: 'الفئة',
      dataIndex: 'category',
      key: 'category',
       render: (category: string) => <Tag color="blue">{category}</Tag>,
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: getStatusTag
    },
    {
      title: 'عدد الاستخدام',
      dataIndex: 'usage_count',
      key: 'usage_count',
      align: 'center',
    },
    {
      title: 'آخر تحديث',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: LegislativeSource) => (
        <Space>
          <Tooltip title="عرض التفاصيل">
            <Button size="small" icon={<EyeOutlined />} onClick={() => showViewDrawer(record)} />
          </Tooltip>
          <Tooltip title="تعديل">
            <Button size="small" icon={<EditOutlined />} onClick={() => showEditModal(record)} />
          </Tooltip>
          {record.status === 'pending_review' && (
            <Tooltip title="اعتماد المصدر">
                <Button size="small" icon={<CheckCircleOutlined />} onClick={() => handleApprove(record.id)} />
            </Tooltip>
          )}
          <Popconfirm title="هل أنت متأكد من الحذف؟" onConfirm={() => handleDelete(record.id)}>
            <Tooltip title="حذف">
              <Button size="small" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card bordered={false} style={{ borderTopLeftRadius: 0 }}>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* Filter and Action Bar */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Input
              placeholder="ابحث في العنوان أو المحتوى..."
              prefix={<SearchOutlined />}
              style={{ width: 250 }}
              onChange={(e) => setSearchText(e.target.value)}
            />
            <Select
              placeholder="تصفية حسب الحالة"
              style={{ width: 150 }}
              allowClear
              onChange={(value) => setStatusFilter(value as DataSourceStatus)}
            >
              <Option value="approved">معتمد</Option>
              <Option value="pending_review">قيد المراجعة</Option>
              <Option value="rejected">مرفوض</Option>
              <Option value="outdated">قديم</Option>
            </Select>
            <Button icon={<FilterOutlined />}>إعادة تعيين</Button>
          </Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
            إضافة مصدر جديد
          </Button>
        </div>
        
        {/* Table */}
        <Table
          columns={columns}
          dataSource={sources}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size);
            },
          }}
        />
      </Space>

      {/* Add/Edit Modal */}
      <Modal
        title={editingSource ? 'تعديل المصدر التشريعي' : 'إضافة مصدر تشريعي جديد'}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={onFinish} initialValues={{ status: 'pending_review' }}>
          <Row gutter={16}>
            <Col span={8}><Form.Item name="article_number" label="رقم المادة"><Input /></Form.Item></Col>
            <Col span={16}><Form.Item name="title" label="العنوان" rules={[{ required: true }]}><Input /></Form.Item></Col>
          </Row>
          <Form.Item name="content" label="المحتوى" rules={[{ required: true }]}><Input.TextArea rows={6} /></Form.Item>
          <Row gutter={16}>
            <Col span={12}><Form.Item name="source_document" label="المصدر (النظام/اللائحة)" rules={[{ required: true }]}><Input /></Form.Item></Col>
            <Col span={12}><Form.Item name="category" label="الفئة" rules={[{ required: true }]}><Input /></Form.Item></Col>
          </Row>
          <Form.Item name="tags" label="الوسوم (مفصولة بفاصلة)"><Input /></Form.Item>
          <Form.Item name="status" label="الحالة" rules={[{ required: true }]}>
            <Select>
              <Option value="pending_review">قيد المراجعة</Option>
              <Option value="approved">معتمد</Option>
            </Select>
          </Form.Item>
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={handleCancel}>إلغاء</Button>
              <Button type="primary" htmlType="submit">حفظ</Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* View Drawer */}
      <Drawer width={600} open={isDrawerVisible} onClose={() => setIsDrawerVisible(false)} title="تفاصيل المصدر">
        {viewingSource && (
          <>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="رقم المادة">{viewingSource.article_number}</Descriptions.Item>
              <Descriptions.Item label="العنوان">{viewingSource.title}</Descriptions.Item>
              <Descriptions.Item label="المصدر">{viewingSource.source_document}</Descriptions.Item>
              <Descriptions.Item label="الفئة">{viewingSource.category}</Descriptions.Item>
              <Descriptions.Item label="الحالة">{getStatusTag(viewingSource.status)}</Descriptions.Item>
              <Descriptions.Item label="الوسوم">{viewingSource.tags.map(t => <Tag key={t}>{t}</Tag>)}</Descriptions.Item>
              <Descriptions.Item label="المحتوى"><Paragraph>{viewingSource.content}</Paragraph></Descriptions.Item>
              <Descriptions.Item label="عدد الاستخدام">{viewingSource.usage_count}</Descriptions.Item>
              <Descriptions.Item label="تاريخ الإنشاء">{dayjs(viewingSource.created_at).format('YYYY-MM-DD')}</Descriptions.Item>
              <Descriptions.Item label="آخر تحديث">{dayjs(viewingSource.updated_at).format('YYYY-MM-DD')}</Descriptions.Item>
            </Descriptions>
            <Divider>العلاقات القانونية (Graph)</Divider>
            <GraphVisualizer articleId={viewingSource.id} />
            <Divider>السجل التاريخي</Divider>
            <HistoryVisualizer articleId={viewingSource.id} />
          </>
        )}
      </Drawer>
    </Card>
  );
}; 
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  message,
  Modal,
  Form,
  Input,
  Typography,
  Space,
  Popconfirm,
  Tooltip,
  Upload,
  Divider,
  Tag,
  Tabs
} from 'antd';
import {
  CloudUploadOutlined,
  CloudDownloadOutlined,
  CloudSyncOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  FileOutlined,
  HistoryOutlined,
  ImportOutlined,
  ExportOutlined,
  ReloadOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { useTranslation } from '../../hooks/use-translation';
import { UIBackupService, UIBackupInDB } from '../../services/ui-backup-service';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Dragger } = Upload;

interface BackupManagerProps {
  onBackupCreated?: () => void;
  onBackupRestored?: () => void;
}

export const BackupManager: React.FC<BackupManagerProps> = ({
  onBackupCreated,
  onBackupRestored
}) => {
  const { t, language } = useTranslation();
  const [backups, setBackups] = useState<UIBackupInDB[]>([]);
  const [systemBackups, setSystemBackups] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [restoreModalVisible, setRestoreModalVisible] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState<UIBackupInDB | null>(null);
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('1');

  const fetchBackups = async () => {
    setLoading(true);
    try {
      const data = await UIBackupService.getBackups();
      setBackups(data);

      // محاولة الحصول على النسخ الاحتياطية الشاملة للنظام
      try {
        const systemData = await UIBackupService.getSystemBackups();
        setSystemBackups(systemData);
      } catch (systemError) {
        console.error('خطأ في جلب النسخ الاحتياطية الشاملة:', systemError);
      }

    } catch (error) {
      console.error('خطأ في جلب النسخ الاحتياطية:', error);
      message.error(t('errorFetchingBackups', 'خطأ في جلب النسخ الاحتياطية'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBackups();
  }, []);

  const handleCreateBackup = async () => {
    try {
      form.validateFields().then(async (values) => {
        setLoading(true);
        try {
          await UIBackupService.createBackup(values.description);
          message.success(t('backupCreated', 'تم إنشاء النسخة الاحتياطية بنجاح'));
          fetchBackups();
          setCreateModalVisible(false);
          form.resetFields();
          if (onBackupCreated) onBackupCreated();
        } catch (error) {
          console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
          message.error(t('errorCreatingBackup', 'خطأ في إنشاء النسخة الاحتياطية'));
        } finally {
          setLoading(false);
        }
      });
    } catch (error) {
      // تجاهل أخطاء التحقق من الحقول
    }
  };

  const handleCreateSystemBackup = async () => {
    try {
      form.validateFields().then(async (values) => {
        setLoading(true);
        try {
          await UIBackupService.createSystemBackup(values.description);
          message.success(t('systemBackupCreated', 'تم إنشاء النسخة الاحتياطية الشاملة بنجاح'));
          fetchBackups();
          setCreateModalVisible(false);
          form.resetFields();
        } catch (error) {
          console.error('خطأ في إنشاء النسخة الاحتياطية الشاملة:', error);
          message.error(t('errorCreatingSystemBackup', 'خطأ في إنشاء النسخة الاحتياطية الشاملة'));
        } finally {
          setLoading(false);
        }
      });
    } catch (error) {
      // تجاهل أخطاء التحقق من الحقول
    }
  };

  const handleRestoreBackup = async () => {
    if (!selectedBackup) return;

    setLoading(true);
    try {
      await UIBackupService.restoreBackup({ backup_id: selectedBackup.id });
      message.success(t('backupRestored', 'تم استعادة النسخة الاحتياطية بنجاح'));
      setRestoreModalVisible(false);
      if (onBackupRestored) onBackupRestored();
    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      message.error(t('errorRestoringBackup', 'خطأ في استعادة النسخة الاحتياطية'));
    } finally {
      setLoading(false);
    }
  };

  const handleRestoreSystemBackup = async (backupPath: string) => {
    Modal.confirm({
      title: t('confirmSystemRestore', 'تأكيد استعادة النظام'),
      icon: <ExclamationCircleOutlined />,
      content: t('systemRestoreWarning', 'سيؤدي هذا إلى استعادة جميع بيانات النظام، بما في ذلك قاعدة البيانات وإعدادات واجهة المستخدم. هل أنت متأكد من المتابعة؟'),
      okText: t('restore', 'استعادة'),
      cancelText: t('cancel', 'إلغاء'),
      onOk: async () => {
        setLoading(true);
        try {
          await UIBackupService.restoreSystemBackup(backupPath);
          message.success(t('systemBackupRestored', 'تم استعادة النظام بنجاح. قد تحتاج إلى إعادة تحميل الصفحة.'));
          setTimeout(() => window.location.reload(), 2000);
        } catch (error) {
          console.error('خطأ في استعادة النظام:', error);
          message.error(t('errorRestoringSystem', 'خطأ في استعادة النظام'));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleDeleteBackup = async (backupId: number) => {
    setLoading(true);
    try {
      await UIBackupService.deleteBackup(backupId);
      message.success(t('backupDeleted', 'تم حذف النسخة الاحتياطية بنجاح'));
      fetchBackups();
    } catch (error) {
      console.error('خطأ في حذف النسخة الاحتياطية:', error);
      message.error(t('errorDeletingBackup', 'خطأ في حذف النسخة الاحتياطية'));
    } finally {
      setLoading(false);
    }
  };

  const handleExportBackup = async (backupId: number) => {
    try {
      const blob = await UIBackupService.exportBackup(backupId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `backup-${backupId}-${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();
      message.success(t('backupExported', 'تم تصدير النسخة الاحتياطية بنجاح'));
    } catch (error) {
      console.error('خطأ في تصدير النسخة الاحتياطية:', error);
      message.error(t('errorExportingBackup', 'خطأ في تصدير النسخة الاحتياطية'));
    }
  };

  const handleImportBackup = async (file: File) => {
    setLoading(true);
    try {
      await UIBackupService.importBackup(file);
      message.success(t('backupImported', 'تم استيراد النسخة الاحتياطية بنجاح'));
      fetchBackups();
    } catch (error) {
      console.error('خطأ في استيراد النسخة الاحتياطية:', error);
      message.error(t('errorImportingBackup', 'خطأ في استيراد النسخة الاحتياطية'));
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      return format(date, 'PPpp', { locale: language === 'ar' ? ar : enUS });
    } catch (e) {
      return dateStr;
    }
  };

  const uiBackupColumns = [
    {
      title: t('id', 'المعرف'),
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: t('createdAt', 'تاريخ الإنشاء'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => formatDate(text),
      sorter: (a: UIBackupInDB, b: UIBackupInDB) => {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      },
      defaultSortOrder: 'descend' as 'descend',
    },
    {
      title: t('description', 'الوصف'),
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '-',
    },
    {
      title: t('actions', 'الإجراءات'),
      key: 'actions',
      render: (_: any, record: UIBackupInDB) => (
        <Space size="small">
          <Tooltip title={t('restore', 'استعادة')}>
            <Button
              type="link"
              icon={<CloudDownloadOutlined />}
              onClick={() => {
                setSelectedBackup(record);
                setRestoreModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t('export', 'تصدير')}>
            <Button
              type="link"
              icon={<ExportOutlined />}
              onClick={() => handleExportBackup(record.id)}
            />
          </Tooltip>
          <Tooltip title={t('delete', 'حذف')}>
            <Popconfirm
              title={t('confirmDelete', 'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')}
              onConfirm={() => handleDeleteBackup(record.id)}
              okText={t('yes', 'نعم')}
              cancelText={t('no', 'لا')}
            >
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const systemBackupColumns = [
    {
      title: t('timestamp', 'التاريخ'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text: string) => formatDate(text),
      sorter: (a: any, b: any) => {
        return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
      },
      defaultSortOrder: 'descend' as 'descend',
    },
    {
      title: t('path', 'المسار'),
      dataIndex: 'path',
      key: 'path',
      ellipsis: true,
    },
    {
      title: t('size', 'الحجم'),
      dataIndex: 'size',
      key: 'size',
      render: (size: number) => {
        // تحويل الحجم من بايت إلى ميجابايت أو جيجابايت
        if (size < 1024 * 1024) {
          return `${(size / 1024).toFixed(2)} KB`;
        } else if (size < 1024 * 1024 * 1024) {
          return `${(size / (1024 * 1024)).toFixed(2)} MB`;
        } else {
          return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
        }
      },
    },
    {
      title: t('description', 'الوصف'),
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '-',
    },
    {
      title: t('actions', 'الإجراءات'),
      key: 'actions',
      render: (_: any, record: any) => (
        <Space size="small">
          <Tooltip title={t('restore', 'استعادة')}>
            <Button
              type="link"
              icon={<CloudDownloadOutlined />}
              onClick={() => handleRestoreSystemBackup(record.path)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.json',
    showUploadList: false,
    beforeUpload: (file: File) => {
      handleImportBackup(file);
      return false;
    },
  };

  return (
    <Card>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <CloudSyncOutlined />
              {t('uiBackups', 'نسخ إعدادات واجهة المستخدم')}
            </span>
          }
          key="1"
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Space>
              <Button
                type="primary"
                icon={<CloudUploadOutlined />}
                onClick={() => {
                  form.resetFields();
                  setCreateModalVisible(true);
                }}
              >
                {t('createBackup', 'إنشاء نسخة احتياطية')}
              </Button>
              <Upload {...uploadProps}>
                <Button icon={<ImportOutlined />}>
                  {t('importBackup', 'استيراد نسخة احتياطية')}
                </Button>
              </Upload>
              <Button icon={<ReloadOutlined />} onClick={fetchBackups}>
                {t('refresh', 'تحديث')}
              </Button>
            </Space>

            <Table
              dataSource={backups}
              columns={uiBackupColumns}
              rowKey="id"
              loading={loading}
              pagination={{ defaultPageSize: 10 }}
              locale={{
                emptyText: t('noBackups', 'لا توجد نسخ احتياطية'),
              }}
            />
          </Space>
        </TabPane>

        <TabPane
          tab={
            <span>
              <SaveOutlined />
              {t('systemBackups', 'النسخ الاحتياطية الشاملة')}
            </span>
          }
          key="2"
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Space>
              <Button
                type="primary"
                icon={<CloudUploadOutlined />}
                onClick={() => {
                  form.resetFields();
                  setCreateModalVisible(true);
                }}
              >
                {t('createSystemBackup', 'إنشاء نسخة احتياطية شاملة')}
              </Button>
              <Button icon={<ReloadOutlined />} onClick={fetchBackups}>
                {t('refresh', 'تحديث')}
              </Button>
            </Space>

            <Table
              dataSource={systemBackups}
              columns={systemBackupColumns}
              rowKey="path"
              loading={loading}
              pagination={{ defaultPageSize: 10 }}
              locale={{
                emptyText: t('noSystemBackups', 'لا توجد نسخ احتياطية شاملة'),
              }}
            />
          </Space>
        </TabPane>
      </Tabs>

      {/* نافذة إنشاء نسخة احتياطية */}
      <Modal
        title={
          activeTab === '1'
            ? t('createBackup', 'إنشاء نسخة احتياطية لإعدادات واجهة المستخدم')
            : t('createSystemBackup', 'إنشاء نسخة احتياطية شاملة للنظام')
        }
        open={createModalVisible}
        onOk={activeTab === '1' ? handleCreateBackup : handleCreateSystemBackup}
        onCancel={() => setCreateModalVisible(false)}
        confirmLoading={loading}
        okText={t('create', 'إنشاء')}
        cancelText={t('cancel', 'إلغاء')}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="description"
            label={t('backupDescription', 'وصف النسخة الاحتياطية')}
            rules={[{ max: 255, message: t('descriptionTooLong', 'الوصف طويل جدًا') }]}
          >
            <Input.TextArea
              placeholder={t('backupDescriptionPlaceholder', 'وصف اختياري للنسخة الاحتياطية')}
              rows={4}
            />
          </Form.Item>
          {activeTab === '2' && (
            <Alert
              type="warning"
              message={t('systemBackupWarning', 'تحذير')}
              description={t(
                'systemBackupWarningDesc',
                'إنشاء نسخة احتياطية شاملة قد يستغرق بعض الوقت وقد يؤثر مؤقتًا على أداء النظام.'
              )}
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}
        </Form>
      </Modal>

      {/* نافذة استعادة النسخة الاحتياطية */}
      <Modal
        title={t('restoreBackup', 'استعادة النسخة الاحتياطية')}
        open={restoreModalVisible}
        onOk={handleRestoreBackup}
        onCancel={() => setRestoreModalVisible(false)}
        confirmLoading={loading}
        okText={t('restore', 'استعادة')}
        cancelText={t('cancel', 'إلغاء')}
      >
        <Typography.Paragraph>
          {t(
            'restoreConfirmation',
            'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع إعدادات واجهة المستخدم الحالية.'
          )}
        </Typography.Paragraph>
        {selectedBackup && (
          <>
            <Typography.Text strong>{t('backupDetails', 'تفاصيل النسخة الاحتياطية')}:</Typography.Text>
            <div style={{ background: '#f5f5f5', padding: 12, borderRadius: 4, marginTop: 8 }}>
              <p>
                <strong>{t('id', 'المعرف')}:</strong> {selectedBackup.id}
              </p>
              <p>
                <strong>{t('createdAt', 'تاريخ الإنشاء')}:</strong> {formatDate(selectedBackup.created_at)}
              </p>
              <p>
                <strong>{t('description', 'الوصف')}:</strong> {selectedBackup.description || '-'}
              </p>
            </div>
          </>
        )}
      </Modal>
    </Card>
  );
};

import React from 'react';

export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex items-start gap-4 mb-6 animate-fade-in">
      <div className="flex-shrink-0 w-12 h-12 bg-primary rounded-full flex items-center justify-center shadow-md">
        <img src="/ShuraihAIUI.svg" alt="شُريح" className="w-7 h-7 logo-component" />
      </div>
      
      <div className="bg-card px-6 py-4 rounded-2xl border border-border shadow-sm">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
      </div>
    </div>
  );
};
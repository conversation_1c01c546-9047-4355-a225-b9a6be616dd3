import React, { useState, useRef } from 'react';
import { message as antMessage } from 'antd';
import { useTranslation } from '../../hooks/use-translation';
import { cn } from '../../lib/utils';
import { Copy, Loader2, X } from 'lucide-react';

export interface LegalReference {
  id: string;
  title: string;
  content: string;
  source: string;
  section?: string;
  articleNumber: string;
}

interface LegalCitationProps {
  articleNumber: string;
  children: React.ReactNode;
  className?: string;
}

interface CitationPopupState {
  show: boolean;
  title: string;
  content: string;
  position: { top: number; left: number };
  source: string;
  section: string;
  loading: boolean;
  error?: string;
}

export function LegalCitation({ articleNumber, children, className }: LegalCitationProps) {
  const { t } = useTranslation();
  const [citationPopup, setCitationPopup] = useState<CitationPopupState>({
    show: false,
    title: '',
    content: '',
    position: { top: 0, left: 0 },
    source: '',
    section: '',
    loading: false
  });
  
  const popupRef = useRef<HTMLDivElement>(null);
  
  // مثال لبيانات الاستشهادات (في التطبيق الحقيقي ستأتي من API)
  const citations: Record<string, LegalReference> = {
    '11': {
      id: 'legal_11',
      title: 'المادة 11 من نظام إيجار العقارات',
      content: 'لا يجوز للمؤجر زيادة الأجرة خلال مدة العقد ما لم يتم الاتفاق على ذلك صراحةً في العقد.',
      source: 'نظام إيجار العقارات، الصادر بالمرسوم الملكي رقم (م/61) وتاريخ 1427/09/18هـ',
      section: 'الباب الثاني - الأجرة',
      articleNumber: '11'
    },
    '57': {
      id: 'legal_57',
      title: 'المادة 57 من نظام المرافعات الشرعية',
      content: 'إذا غاب المدعى عليه عن الجلسة الأولى ولم يكن قد تبلغ لشخصه أو وكيله في الدعوى نفسها، وتم تبليغه لاحقاً ولم يحضر دون عذر تقبله المحكمة، فإن المحكمة تنظر في الدعوى وتصدر حكمها غيابياً.',
      source: 'نظام المرافعات الشرعية، الصادر بالمرسوم الملكي رقم (م/1) وتاريخ 1435/01/22هـ',
      section: 'الباب الخامس - الأحكام الغيابية',
      articleNumber: '57'
    },
    '77': {
      id: 'legal_77',
      title: 'المادة 77 من نظام العمل',
      content: 'لا يجوز لصاحب العمل فسخ العقد دون مكافأة أو إشعار العامل أو تعويضه إلا في الحالات الواردة في هذه المادة، وبشرط أن تتاح للعامل الفرصة لكي يبدي أسباب معارضته للفسخ.',
      source: 'نظام العمل، الصادر بالمرسوم الملكي رقم (م/51) وتاريخ 1426/08/23هـ',
      section: 'الباب السادس - انتهاء عقد العمل',
      articleNumber: '77'
    }
  };
  
  const handleCitationClick = (e: React.MouseEvent) => {
    e.preventDefault();
    
    // حساب موقع النافذة المنبثقة
    const rect = e.currentTarget.getBoundingClientRect();
    const position = {
      top: rect.bottom + window.scrollY + 10,
      left: rect.left + window.scrollX
    };
    
    // عرض حالة التحميل أولاً
    setCitationPopup({
      show: true,
      title: `المادة ${articleNumber}`,
      content: '',
      position,
      source: '',
      section: '',
      loading: true
    });
    
    // محاكاة طلب API
    setTimeout(() => {
      const citation = citations[articleNumber];
      
      if (citation) {
        setCitationPopup({
          show: true,
          title: citation.title,
          content: citation.content,
          position,
          source: citation.source,
          section: citation.section || '',
          loading: false
        });
      } else {
        setCitationPopup({
          show: true,
          title: `المادة ${articleNumber}`,
          content: 'لم يتم العثور على هذه المادة في قاعدة البيانات.',
          position,
          source: '',
          section: '',
          loading: false,
          error: 'not_found'
        });
      }
    }, 500);
    
    // إضافة مستمع لإغلاق النافذة عند النقر خارجها
    document.addEventListener('click', handleClickOutside);
  };
  
  const handleClickOutside = (e: MouseEvent) => {
    if (popupRef.current && !popupRef.current.contains(e.target as Node)) {
      setCitationPopup(prev => ({ ...prev, show: false }));
      document.removeEventListener('click', handleClickOutside);
    }
  };
  
  const copyCitationToClipboard = () => {
    const textToCopy = `${citationPopup.title}\n\n${citationPopup.content}\n\nالمصدر: ${citationPopup.source}`;
    navigator.clipboard.writeText(textToCopy);
    
    // إظهار رسالة نجاح باستخدام Ant Design message
    antMessage.success('تم نسخ الاستشهاد');
  };
  
  return (
    <>
      <button
        onClick={handleCitationClick}
        className={cn(
          "inline font-medium transition-colors duration-200 cursor-pointer border-none bg-transparent p-0 underline decoration-2 underline-offset-2",
          "text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-sm legal-citation-button",
          className
        )}
        title={`عرض تفاصيل المادة ${articleNumber}`}
        aria-label={`عرض تفاصيل المادة ${articleNumber}`}
      >
        {children}
      </button>
      
      {/* النافذة المنبثقة للاستشهاد */}
      {citationPopup.show && (
        <div 
          ref={popupRef}
          className="fixed z-50 bg-popover rounded-lg shadow-xl overflow-hidden transform transition-all duration-300 ease-out citation-popup-enter citation-popup"
          dir="rtl"
          style={{
            top: `${citationPopup.position.top}px`,
            left: `${citationPopup.position.left}px`,
            width: window.innerWidth < 768 ? '90vw' : '500px',
            maxWidth: '90vw',
            maxHeight: '80vh'
          }}
        >
          {/* رأس النافذة */}
          <div 
            className="flex justify-between items-center px-4 py-3 bg-muted/50 border-b border-border citation-header"
          >
            <div className="font-semibold text-base flex items-center gap-2 text-primary">
              {citationPopup.loading && <Loader2 className="w-4 h-4 animate-spin" />}
              <span className="truncate">{citationPopup.title}</span>
            </div>
            <div className="flex gap-2 flex-shrink-0">
              {!citationPopup.loading && !citationPopup.error && (
                <button
                  onClick={copyCitationToClipboard}
                  className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                  title="نسخ النص"
                  aria-label="نسخ النص"
                >
                  <Copy className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={() => setCitationPopup(prev => ({ ...prev, show: false }))}
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                title="إغلاق"
                aria-label="إغلاق"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* محتوى النافذة */}
          <div className="px-6 py-5 text-right overflow-y-auto" style={{ maxHeight: 'calc(80vh - 80px)' }}>
            {citationPopup.loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin text-primary" />
                <span className="mr-2 text-muted-foreground">جاري التحميل...</span>
              </div>
            ) : citationPopup.error ? (
              <div className="py-4">
                <div className="text-destructive text-center">
                  {citationPopup.content}
                </div>
              </div>
            ) : (
              <>
                <div 
                  className="text-popover-foreground leading-relaxed text-base mb-6" 
                  style={{ lineHeight: '1.8' }}
                >
                  {citationPopup.content}
                </div>
                
                {/* معلومات المصدر */}
                {citationPopup.source && (
                  <div className="pt-4 border-t border-border">
                    <div className="text-sm text-muted-foreground">
                      <strong>المصدر:</strong> {citationPopup.source}
                    </div>
                    {citationPopup.section && (
                      <div className="text-sm text-muted-foreground mt-1">
                        <strong>القسم:</strong> {citationPopup.section}
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
}

export function parseTextWithCitations(text: string): React.ReactNode[] {
  const citationRegex = /المادة\s*\((\d+)\)/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;
  let key = 0;
  
  while ((match = citationRegex.exec(text)) !== null) {
    // إضافة النص قبل الاستشهاد
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }
    
    // إضافة الاستشهاد
    const articleNumber = match[1];
    parts.push(
      <LegalCitation key={key++} articleNumber={articleNumber}>
        المادة ({articleNumber})
      </LegalCitation>
    );
    
    lastIndex = match.index + match[0].length;
  }
  
  // إضافة باقي النص
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  
  return parts;
}
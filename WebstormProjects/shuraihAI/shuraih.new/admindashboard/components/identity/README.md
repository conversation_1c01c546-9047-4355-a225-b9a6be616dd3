# 🎨 الهوية البصرية لاسم شُرَيح

## 📖 نظرة عامة

هذا الملف يحتوي على الهوية البصرية الخاصة باسم **شُرَيح الذكاء الاصطناعي القانوني السعودي**. تم تصميم هذه الهوية لتكون متسقة ومتاحة للجميع، مع دعم عدم التمييز اللوني وسياق النصوص المختلفة.

## 🎯 المبادئ الأساسية

### ✅ **سياق اللون في النصوص**
- الاسم يأخذ لون النص المحيط عندما يكون في سياق نص عادي
- يظهر باللون الأساسي `#2D69F6` في العناوين والمواضع المميزة
- يتكيف مع الخلفيات الداكنة والفاتحة تلقائياً

### ♿ **عدم التمييز اللوني**
- يدعم وضع التباين العالي
- يحافظ على الوضوح في جميع الحالات
- مناسب للطباعة بالأبيض والأسود

### 🔤 **الخط والطباعة**
- يستخدم خط **IBM Plex Sans Arabic** للوضوح الأمثل
- يدعم الأوزان: عادي (400)، متوسط (600)، عريض (700)
- يحافظ على الوضوح في جميع الأحجام

## 🚀 كيفية الاستخدام

### 📦 **استيراد المكونات**

```tsx
import { 
  ShurayuName, 
  ShurayuNameInline, 
  ShurayuNameTitle,
  useShurayuName 
} from './components/identity';
```

### 🔧 **الاستخدام الأساسي**

#### **في العناوين:**
```tsx
<h1>
  مرحباً بك في <ShurayuNameTitle size="xl" />
</h1>
```

#### **في النصوص العادية:**
```tsx
<p>
  يقدم <ShurayuNameInline /> حلول قانونية ذكية ومتطورة
</p>
```

#### **كرابط:**
```tsx
<ShurayuNameLink onClick={() => navigate('/')} />
```

#### **استخدام متقدم:**
```tsx
<ShurayuName 
  variant="title" 
  size="2xl"
  onDarkBackground={true}
  className="custom-class"
/>
```

### 🎨 **الأنواع المتاحة**

| النوع | الوصف | الاستخدام |
|-------|--------|----------|
| `default` | الشكل الأساسي | العرض العام |
| `title` | للعناوين | `<h1>`, `<h2>`, إلخ |
| `inline-text` | للنصوص العادية | داخل الفقرات |
| `link` | للروابط | العناصر القابلة للنقر |
| `text-context` | يأخذ لون النص | الاندماج مع النص |
| `secondary-text` | للنصوص الثانوية | النصوص الفرعية |

### 📏 **الأحجام المتاحة**

| الحجم | القيمة | الاستخدام |
|-------|-------|----------|
| `xs` | 14px | النصوص الصغيرة |
| `sm` | 16px | النصوص العادية |
| `md` | 20px | النصوص المتوسطة |
| `lg` | 24px | العناوين الفرعية |
| `xl` | 32px | العناوين الرئيسية |
| `2xl` | 40px | العناوين الكبيرة |
| `3xl` | 48px | العناوين العملاقة |

## 🎨 **الألوان والمتغيرات**

### **الألوان الأساسية:**
```css
--shurayh-primary-color: #2D69F6;     /* اللون الأساسي */
--shurayh-secondary-color: #E7E8EF;   /* اللون الثانوي */
--shurayh-bg-color: #F8F8FA;          /* خلفية الصفحة */
--shurayh-text-color: #152442;        /* لون النص */
```

### **الكلاسات المساعدة:**
```css
.shurayh-color-primary    /* اللون الأساسي */
.shurayh-color-secondary  /* اللون الثانوي */
.shurayh-bg-primary       /* خلفية أساسية */
.shurayh-gradient-bg      /* خلفية متدرجة */
.shurayh-font             /* الخط الأساسي */
```

## 📱 **التكيف والاستجابة**

### **دعم الثيمات:**
```tsx
// يتكيف تلقائياً مع الثيم المظلم/الفاتح
<div data-theme="dark">
  <ShurayuName /> {/* سيظهر باللون الفاتح */}
</div>
```

### **دعم التباين العالي:**
```css
@media (prefers-contrast: high) {
  .shurayh-name {
    font-weight: 700;
    text-shadow: 0 0 1px currentColor;
  }
}
```

### **دعم تقليل الحركة:**
```css
@media (prefers-reduced-motion: reduce) {
  .shurayh-name {
    transition: none;
  }
}
```

## 🧰 **Hook مساعد**

```tsx
const { name, fullName, ShurayuName } = useShurayuName();

// name = "شُرَيح"
// fullName = "شُرَيح الذكاء الاصطناعي القانوني السعودي"
```

## 📋 **أمثلة عملية**

### **في لوحة التحكم:**
```tsx
<Title level={2}>
  مرحباً بك في <ShurayuName variant="title" />
</Title>
```

### **في النصوص الوصفية:**
```tsx
<Text type="secondary">
  يوفر <ShurayuName variant="text-context" /> حلول قانونية متطورة
</Text>
```

### **في القوائم:**
```tsx
<Menu.Item>
  <ShurayuName variant="inline-text" /> - الصفحة الرئيسية
</Menu.Item>
```

### **في البطاقات:**
```tsx
<Card title={
  <span>
    إعدادات <ShurayuName variant="text-context" />
  </span>
}>
  المحتوى...
</Card>
```

## ⚠️ **ملاحظات مهمة**

1. **لا تستبدل الشعارات الحالية** - هذه الهوية خاصة بالنصوص والكتابات فقط
2. **استخدم `variant="text-context"`** عندما تريد الاندماج مع النص المحيط
3. **استخدم `onDarkBackground={true}`** للخلفيات الداكنة
4. **تجنب استخدام ألوان مخصصة** للحفاظ على الاتساق

## 🔧 **استكشاف الأخطاء**

### **المشكلة: الخط لا يظهر بشكل صحيح**
```css
/* تأكد من استيراد ملف CSS */
@import './styles/shurayh-identity.css';
```

### **المشكلة: اللون لا يتكيف مع الثيم**
```tsx
/* استخدم الكلاس الصحيح */
<ShurayuName variant="text-context" />
```

### **المشكلة: الحجم غير صحيح**
```tsx
/* حدد الحجم بوضوح */
<ShurayuName size="lg" />
```

---

**📞 للدعم:** تواصل مع فريق تطوير شُرَيح الذكاء الاصطناعي القانوني السعودي 
/**
 * مثال توضيحي لاستخدام الهوية البصرية - شُرَيح الذكاء الاصطناعي القانوني السعودي
 * ==================================================================================
 * 
 * هذا المثال يوضح كيفية استخدام مكونات الهوية البصرية في سياقات مختلفة
 * 
 * المؤلف: فريق تطوير شُرَيح
 * التاريخ: 22 يناير 2025
 */

import React from 'react';
import { Card, Typography, Button, Space, Divider, Tag } from 'antd';
import { 
  ShurayuName, 
  ShurayuNameInline, 
  ShurayuNameTitle, 
  ShurayuNameLink,
  useShurayuName 
} from './index';

const { Title, Paragraph, Text } = Typography;

/**
 * مكون لعرض أمثلة استخدام الهوية البصرية
 * هذا للاختبار والتوضيح فقط - لا يُستخدم في الإنتاج
 */
export const ShurayuIdentityExamples: React.FC = () => {
  const { name, fullName } = useShurayuName();

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="🎨 أمثلة الهوية البصرية لاسم شُرَيح">
        
        {/* العناوين */}
        <Title level={3}>🏷️ في العناوين</Title>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <Title level={1}>
            مرحباً بك في <ShurayuNameTitle size="2xl" />
          </Title>
          <Title level={2}>
            لوحة تحكم <ShurayuName variant="title" size="xl" />
          </Title>
          <Title level={3}>
            إعدادات <ShurayuName variant="title" size="lg" />
          </Title>
        </Space>

        <Divider />

        {/* النصوص العادية */}
        <Title level={3}>📝 في النصوص العادية</Title>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <Paragraph>
            يقدم <ShurayuNameInline /> حلول قانونية ذكية ومتطورة تعتمد على أحدث تقنيات الذكاء الاصطناعي
            لخدمة المحامين والمستشارين القانونيين في المملكة العربية السعودية.
          </Paragraph>
          
          <Paragraph>
            يمكن لمنصة <ShurayuName variant="text-context" /> أن تساعدك في تحليل العقود القانونية
            وتقديم الاستشارات الفورية وإعداد المذكرات القانونية بكفاءة عالية.
          </Paragraph>
          
          <Text type="secondary">
            تم تطوير <ShurayuName variant="secondary-text" /> وفقاً لأعلى معايير الأمان والخصوصية.
          </Text>
        </Space>

        <Divider />

        {/* الأحجام المختلفة */}
        <Title level={3}>📏 الأحجام المختلفة</Title>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div>صغير جداً (xs): <ShurayuName size="xs" /></div>
          <div>صغير (sm): <ShurayuName size="sm" /></div>
          <div>متوسط (md): <ShurayuName size="md" /></div>
          <div>كبير (lg): <ShurayuName size="lg" /></div>
          <div>كبير جداً (xl): <ShurayuName size="xl" /></div>
          <div>عملاق (2xl): <ShurayuName size="2xl" /></div>
          <div>عملاق جداً (3xl): <ShurayuName size="3xl" /></div>
        </Space>

        <Divider />

        {/* الخلفيات المختلفة */}
        <Title level={3}>🎨 على خلفيات مختلفة</Title>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {/* خلفية فاتحة */}
          <div style={{ 
            padding: '16px', 
            backgroundColor: '#f8f8fa', 
            borderRadius: '8px',
            border: '1px solid #e0e0e0'
          }}>
            خلفية فاتحة: <ShurayuName />
          </div>

          {/* خلفية داكنة */}
          <div style={{ 
            padding: '16px', 
            backgroundColor: '#2D69F6', 
            borderRadius: '8px',
            color: 'white'
          }}>
            خلفية داكنة: <ShurayuName onDarkBackground={true} />
          </div>

          {/* خلفية متدرجة */}
          <div style={{ 
            padding: '16px', 
            background: 'linear-gradient(90deg, #2D69F6 60%, #60D3FF 100%)', 
            borderRadius: '8px',
            color: 'white'
          }}>
            خلفية متدرجة: <ShurayuName onDarkBackground={true} />
          </div>
        </Space>

        <Divider />

        {/* الاستخدام في المكونات */}
        <Title level={3}>🔧 في مكونات Ant Design</Title>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {/* في الأزرار */}
          <Space wrap>
            <Button type="primary">
              تسجيل الدخول إلى <ShurayuName variant="inline-text" />
            </Button>
            <Button>
              حول <ShurayuName variant="inline-text" />
            </Button>
          </Space>

          {/* في التاجات */}
          <Space wrap>
            <Tag color="blue">
              منصة <ShurayuName variant="inline-text" />
            </Tag>
            <Tag color="green">
              <ShurayuName variant="inline-text" /> مُفعل
            </Tag>
          </Space>

          {/* كرابط */}
          <div>
            العودة إلى{' '}
            <ShurayuNameLink 
              onClick={() => console.log('تم النقر على اسم شُرَيح')}
            />
          </div>
        </Space>

        <Divider />

        {/* معلومات إضافية */}
        <Title level={3}>ℹ️ معلومات إضافية</Title>
        <Space direction="vertical" size="small">
          <Text>الاسم المختصر: <Text code>{name}</Text></Text>
          <Text>الاسم الكامل: <Text code>{fullName}</Text></Text>
          <Text type="secondary">
            هذه الأمثلة توضح كيفية استخدام الهوية البصرية بطريقة صحيحة ومتسقة
          </Text>
        </Space>

        <Divider />

        {/* ملاحظة */}
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#fff7e6', 
          borderRadius: '8px',
          border: '1px solid #ffd591'
        }}>
          <Text strong>⚠️ ملاحظة مهمة:</Text>
          <br />
          <Text>
            هذه الهوية البصرية مخصصة للنصوص والكتابات فقط. 
            لا تستبدل الشعارات الحالية حتى يُطلب منك ذلك صراحة.
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default ShurayuIdentityExamples; 
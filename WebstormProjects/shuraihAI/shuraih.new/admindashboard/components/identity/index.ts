/**
 * مؤشر الهوية البصرية - شُرَيح الذكاء الاصطناعي القانوني السعودي
 * ================================================================
 * 
 * ملف تصدير مكونات الهوية البصرية لاسم شُرَيح
 * 
 * المؤلف: فريق تطوير شُرَيح
 * التاريخ: 22 يناير 2025
 */

// تصدير مكونات الاسم
export {
  ShurayuName,
  ShurayuNameInline,
  ShurayuNameTitle,
  ShurayuNameLink,
  useShurayuName,
} from '../ShurayuName';

// تصدير الثوابت
export const SHURAYH_COLORS = {
  primary: '#2D69F6',
  secondary: '#E7E8EF',
  background: '#F8F8FA',
  text: '#152442',
  gradient: 'linear-gradient(90deg, #2D69F6 60%, #60D3FF 100%)',
} as const;

export const SHURAYH_FONTS = {
  family: "'IBM Plex Sans Arabic', 'Tajawal', 'Arial', sans-serif",
  weights: {
    normal: 400,
    medium: 600,
    bold: 700,
  },
} as const;

// تصدير كلاسات CSS
export const SHURAYH_CLASSES = {
  name: 'shurayh-name',
  font: 'shurayh-font',
  colorPrimary: 'shurayh-color-primary',
  colorSecondary: 'shurayh-color-secondary',
  bgPrimary: 'shurayh-bg-primary',
  bgSecondary: 'shurayh-bg-secondary',
  gradient: 'shurayh-gradient-bg',
} as const;

// دالة مساعدة للحصول على الاسم
export const getShurayuName = (type: 'short' | 'full' = 'short') => {
  return type === 'short' 
    ? 'شُرَيح'
    : 'شُرَيح الذكاء الاصطناعي القانوني السعودي';
};

// دالة مساعدة لبناء كلاسات CSS
export const buildShurayuClasses = (options: {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  variant?: 'default' | 'title' | 'inline-text' | 'link' | 'text-context' | 'secondary-text';
  onDarkBackground?: boolean;
  extraClasses?: string;
}) => {
  const { size, variant, onDarkBackground, extraClasses } = options;
  
  return [
    SHURAYH_CLASSES.name,
    size && `size-${size}`,
    variant && variant !== 'default' && variant,
    onDarkBackground && 'on-dark-bg',
    extraClasses,
  ].filter(Boolean).join(' ');
}; 
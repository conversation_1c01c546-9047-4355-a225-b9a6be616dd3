import React from 'react';
import { Card, Col, Row, Badge, Statistic, Progress } from 'antd';
import { 
  CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, FileTextOutlined,
  FlagOutlined, CheckOutlined, CloseOutlined, WarningOutlined 
} from '@ant-design/icons';
import { formatNumber } from '../../lib/utils';

/**
 * واجهة خصائص إحصائيات التقارير
 */
interface ReportSummaryCardsProps {
  /** عدد التقارير قيد المراجعة */
  pending: number;
  /** عدد التقارير الموافق عليها */
  approved: number;
  /** عدد التقارير المرفوضة */
  rejected: number;
  /** إجمالي عدد التقارير */
  total: number;
}

/**
 * مكون يعرض بطاقات إحصائية ملخصة لتقارير المحتوى
 */
const ReportSummaryCards: React.FC<ReportSummaryCardsProps> = ({ 
  pending, 
  approved, 
  rejected, 
  total 
}) => {
  // حساب النسب المئوية لكل حالة
  const pendingPercent = total > 0 ? Math.round((pending / total) * 100) : 0;
  const approvedPercent = total > 0 ? Math.round((approved / total) * 100) : 0;
  const rejectedPercent = total > 0 ? Math.round((rejected / total) * 100) : 0;

  return (
    <div className="mb-6">
      <Row gutter={16}>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="h-full">
            <Statistic
              title="إجمالي التقارير"
              value={total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<WarningOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
            <div className="mt-2">
              <Progress 
                percent={100} 
                showInfo={false} 
                strokeColor="#1890ff" 
                trailColor="#f0f0f0" 
              />
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="h-full">
            <Statistic
              title="قيد المراجعة"
              value={pending}
              valueStyle={{ color: '#faad14' }}
              prefix={<FlagOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
            <div className="mt-2">
              <Progress 
                percent={pendingPercent} 
                strokeColor="#faad14" 
                trailColor="#f5f5f5" 
              />
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="h-full">
            <Statistic
              title="تمت الموافقة"
              value={approved}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
            <div className="mt-2">
              <Progress 
                percent={approvedPercent} 
                strokeColor="#52c41a" 
                trailColor="#f5f5f5" 
              />
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="h-full">
            <Statistic
              title="تم الرفض"
              value={rejected}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<CloseOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
            <div className="mt-2">
              <Progress 
                percent={rejectedPercent} 
                strokeColor="#ff4d4f" 
                trailColor="#f5f5f5" 
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ReportSummaryCards;

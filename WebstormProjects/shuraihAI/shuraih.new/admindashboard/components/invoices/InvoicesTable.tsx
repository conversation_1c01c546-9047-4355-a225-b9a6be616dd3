import React, { useState, useEffect } from 'react';
import { Table, Input, Button, DatePicker, Select, Tooltip, message, Space, Tag } from 'antd';
import { SearchOutlined, ReloadOutlined, DownloadOutlined, FilePdfOutlined, MailOutlined } from '@ant-design/icons';
import { invoiceService } from '../../services/invoice-service';
import moment from 'moment';

const { RangePicker } = DatePicker;
const { Option } = Select;

const InvoicesTable: React.FC = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    dateRange: null
  });

  // تحميل البيانات
  const fetchInvoices = async (page = 1, pageSize = 10, filters = {}) => {
    setLoading(true);
    try {
      const response = await invoiceService.getInvoices(filters, page, pageSize);
      setInvoices(response.data);
      setPagination({
        ...pagination,
        current: page,
        pageSize,
        total: response.total
      });
    } catch (error) {
      message.error('فشل في تحميل بيانات الفواتير');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvoices(pagination.current, pagination.pageSize, buildFilters());
  }, []);

  // بناء كائن التصفية للإرسال إلى API
  const buildFilters = () => {
    const queryFilters = {};

    if (filters.search) {
      queryFilters.search = filters.search;
    }

    if (filters.status) {
      queryFilters.status = filters.status;
    }

    if (filters.dateRange && filters.dateRange.length === 2) {
      queryFilters.start_date = filters.dateRange[0].format('YYYY-MM-DD');
      queryFilters.end_date = filters.dateRange[1].format('YYYY-MM-DD');
    }

    return queryFilters;
  };

  // التعامل مع تغيير الصفحة
  const handleTableChange = (pagination) => {
    fetchInvoices(pagination.current, pagination.pageSize, buildFilters());
  };

  // التعامل مع البحث
  const handleSearch = () => {
    fetchInvoices(1, pagination.pageSize, buildFilters());
  };

  // التعامل مع إعادة تعيين التصفية
  const handleReset = () => {
    setFilters({
      search: '',
      status: '',
      dateRange: null
    });
    fetchInvoices(1, pagination.pageSize, {});
  };

  // توليد ملف PDF للفاتورة
  const handleGeneratePdf = async (id) => {
    try {
      await invoiceService.generateInvoicePdf(id);
      message.success('تم إنشاء ملف PDF بنجاح');
    } catch (error) {
      message.error('فشل في إنشاء ملف PDF');
    }
  };

  // إرسال الفاتورة بالبريد الإلكتروني
  const handleSendEmail = async (id, email) => {
    try {
      await invoiceService.sendInvoiceEmail(id, email);
      message.success('تم إرسال الفاتورة بنجاح');
    } catch (error) {
      message.error('فشل في إرسال الفاتورة');
    }
  };

  // التعامل مع عرض التفاصيل
  const handleViewDetails = (id) => {
    // التنقل إلى صفحة التفاصيل
    console.log('View details for invoice', id);
  };

  // التعامل مع إلغاء الفاتورة
  const handleVoidInvoice = (id) => {
    // فتح نافذة تأكيد الإلغاء
    console.log('Void invoice', id);
  };

  // تكوين الأعمدة
  const columns = [
    {
      title: 'رقم الفاتورة',
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      render: text => <a>{text}</a>,
    },
    {
      title: 'المستخدم',
      dataIndex: 'user_name',
      key: 'user_name',
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount, record) => (
        <span>{amount} {record.currency}</span>
      ),
    },
    {
      title: 'تاريخ الإصدار',
      dataIndex: 'issue_date',
      key: 'issue_date',
      render: date => moment(date).format('YYYY-MM-DD'),
    },
    {
      title: 'تاريخ الاستحقاق',
      dataIndex: 'due_date',
      key: 'due_date',
      render: date => moment(date).format('YYYY-MM-DD'),
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: status => {
        let color = '';
        let text = '';

        switch (status) {
          case 'paid':
            color = 'green';
            text = 'مدفوعة';
            break;
          case 'unpaid':
            color = 'orange';
            text = 'غير مدفوعة';
            break;
          case 'void':
            color = 'red';
            text = 'ملغاة';
            break;
          case 'draft':
            color = 'blue';
            text = 'مسودة';
            break;
          default:
            color = 'default';
            text = status;
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Button type="link" onClick={() => handleViewDetails(record.id)}>عرض</Button>
          <Tooltip title="توليد PDF">
            <Button 
              type="link" 
              icon={<FilePdfOutlined />} 
              onClick={() => handleGeneratePdf(record.id)} 
            />
          </Tooltip>
          <Tooltip title="إرسال بالبريد الإلكتروني">
            <Button 
              type="link" 
              icon={<MailOutlined />} 
              onClick={() => handleSendEmail(record.id, record.user_email)} 
            />
          </Tooltip>
          {record.status !== 'void' && (
            <Button 
              type="link" 
              danger 
              onClick={() => handleVoidInvoice(record.id)}
            >
              إلغاء
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="invoices-table">
      <div className="table-filters" style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="بحث حسب الرقم أو المستخدم"
            value={filters.search}
            onChange={e => setFilters({ ...filters, search: e.target.value })}
            style={{ width: 240 }}
            suffix={<SearchOutlined />}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="الحالة"
            style={{ width: 120 }}
            value={filters.status}
            onChange={value => setFilters({ ...filters, status: value })}
            allowClear
          >
            <Option value="paid">مدفوعة</Option>
            <Option value="unpaid">غير مدفوعة</Option>
            <Option value="void">ملغاة</Option>
            <Option value="draft">مسودة</Option>
          </Select>

          <RangePicker
            value={filters.dateRange}
            onChange={dates => setFilters({ ...filters, dateRange: dates })}
          />

          <Button type="primary" onClick={handleSearch}>بحث</Button>
          <Button onClick={handleReset} icon={<ReloadOutlined />}>إعادة تعيين</Button>

          <Tooltip title="تنزيل تقرير Excel">
            <Button icon={<DownloadOutlined />} onClick={() => console.log('Download report')}>تنزيل التقرير</Button>
          </Tooltip>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={invoices}
        rowKey="id"
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default InvoicesTable;
